import antfu from '@antfu/eslint-config';

export default antfu(
  {
    // Type of the project
    type: 'app',

    // Enable TypeScript support with project configuration
    typescript: {
      tsconfigPath: 'tsconfig.json',
    },

    // Disable frontend frameworks
    vue: false,
    react: false,
    svelte: false,
    astro: false,
    solid: false,

    // Enable Node.js specific rules
    node: true,

    // Disable formatters (we'll use <PERSON><PERSON><PERSON> separately)
    formatters: false,

    // Configure stylistic rules
    stylistic: {
      indent: 2,
      quotes: 'single',
      semi: true,
    },

    // Ignore patterns
    ignores: [
      'dist/',
      'node_modules/',
      '**/*.spec.ts',
      '**/*.test.ts',
    ],
  },

  // Custom rules for NestJS and financial applications (TypeScript files only)
  {
    files: ['**/*.ts', '**/*.tsx'],
    rules: {
      // TypeScript strict rules for financial applications
      '@typescript-eslint/no-explicit-any': 'error',
      '@typescript-eslint/prefer-nullish-coalescing': 'error',
      '@typescript-eslint/prefer-optional-chain': 'error',
      '@typescript-eslint/no-floating-promises': 'error',
      '@typescript-eslint/await-thenable': 'error',
      '@typescript-eslint/no-misused-promises': 'error',
      '@typescript-eslint/require-await': 'error',
      '@typescript-eslint/no-unnecessary-type-assertion': 'error',
      '@typescript-eslint/prefer-as-const': 'error',
      '@typescript-eslint/switch-exhaustiveness-check': 'error',

      // Code quality and maintainability
      'complexity': ['error', { max: 10 }],
      'max-depth': ['error', 4],
      'max-lines': ['warn', { max: 300, skipBlankLines: true, skipComments: true }],
      'max-lines-per-function': ['warn', { max: 50, skipBlankLines: true, skipComments: true }],
      'max-params': ['error', 4],

      // Security rules for financial applications
      'no-eval': 'error',
      'no-implied-eval': 'error',
      'no-new-func': 'error',
      'no-script-url': 'error',

      // Performance and best practices
      'prefer-const': 'error',
      'no-var': 'error',
      'prefer-arrow-callback': 'error',
      'prefer-template': 'error',
      'no-nested-ternary': 'error',
      'no-unneeded-ternary': 'error',
      'no-else-return': 'error',
      'consistent-return': 'error',
      'no-return-assign': 'error',

      // Allow void for fire-and-forget promises
      'no-void': ['error', { allowAsStatement: true }],

      // Disable some opinionated rules for NestJS
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',

      // Allow magic numbers for common values
      'no-magic-numbers': [
        'warn',
        {
          ignore: [-1, 0, 1, 2],
          ignoreArrayIndexes: true,
          ignoreDefaultValues: true,
        },
      ],
    },
  },

  // NestJS module files can be empty classes
  {
    files: ['**/*.module.ts'],
    rules: {
      '@typescript-eslint/no-extraneous-class': 'off',
    },
  },

  // Test files have relaxed rules
  {
    files: ['**/*.spec.ts', '**/*.test.ts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      'no-magic-numbers': 'off',
      'max-lines-per-function': 'off',
    },
  },
);
