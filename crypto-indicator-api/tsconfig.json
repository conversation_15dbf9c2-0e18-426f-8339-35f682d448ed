{
  "compilerOptions": {
    "incremental": true,
    "target": "ES2021",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "baseUrl": "./",
    "module": "commonjs",

    // Module resolution
    "moduleResolution": "node",

    // Path mapping for clean imports
    "paths": {
      "@/*": ["src/*"],
      "@/dto/*": ["src/dto/*"],
      "@/services/*": ["src/services/*"],
      "@/constants/*": ["src/constants/*"],
      "@/api/*": ["src/api/*"]
    },
    "resolveJsonModule": true,
    // Strict type checking for maintainability
    "strict": true,
    "strictBindCallApply": true,
    "strictFunctionTypes": true,
    "strictNullChecks": true,
    "strictPropertyInitialization": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "exactOptionalPropertyTypes": true,

    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "declaration": true,
    "outDir": "./dist",
    "removeComments": true,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    // Additional checks for code quality
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "test",
    "**/*.spec.ts",
    "**/*.test.ts"
  ]
}
