import { BadRequestException, Controller, Get, Logger, Query } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';

import { CryptoCurrencyData } from './api/financial.indicator.api';
import { API_DOCS, API_ENDPOINTS, API_ERROR_MESSAGES, API_TAGS, QUERY_PARAMS } from './constants/api';
import { CryptoStatisticsService } from './crypto.statistics.service';
import { CryptoCurrencyStatisticsDto } from './dto/crypto-statistics.dto';

@ApiTags(API_TAGS.CRYPTO_STATISTICS)
@Controller()
export class CryptoStatisticsController {
  private readonly logger = new Logger(CryptoStatisticsController.name);

  constructor(
    private readonly cryptoStatisticsService: CryptoStatisticsService,
  ) {
  }

  @Get(API_ENDPOINTS.CRYPTO_STATISTICS)
  @ApiOperation({
    summary: API_DOCS.CRYPTO_STATISTICS.SUMMARY,
    description: API_DOCS.CRYPTO_STATISTICS.DESCRIPTION,
  })
  @ApiOkResponse({ type: [CryptoCurrencyStatisticsDto] })
  async getCryptoStatistics(): Promise<CryptoCurrencyData[]> {
    this.logger.log('Getting crypto statistics');
    return this.cryptoStatisticsService.getStructuredStatistics();
  }

  @Get(API_ENDPOINTS.CRYPTO_INDICATORS)
  @ApiOperation({
    summary: API_DOCS.CRYPTO_INDICATORS.SUMMARY,
    description: API_DOCS.CRYPTO_INDICATORS.DESCRIPTION,
  })
  @ApiOkResponse({
    type: CryptoCurrencyStatisticsDto,
    description: 'Historical indicator data retrieved successfully',
  })
  @ApiQuery({
    name: QUERY_PARAMS.SYMBOL.NAME,
    description: QUERY_PARAMS.SYMBOL.DESCRIPTION,
    example: QUERY_PARAMS.SYMBOL.EXAMPLE,
  })
  @ApiQuery({
    name: QUERY_PARAMS.CONVERSION_CURRENCY.NAME,
    description: QUERY_PARAMS.CONVERSION_CURRENCY.DESCRIPTION,
    example: QUERY_PARAMS.CONVERSION_CURRENCY.EXAMPLE,
  })
  async getCryptoIndicators(
        @Query(QUERY_PARAMS.SYMBOL.NAME) symbol: string,
        @Query(QUERY_PARAMS.CONVERSION_CURRENCY.NAME) conversionCurrency: string,
  ): Promise<CryptoCurrencyData> {
    this.logger.log(`Getting crypto indicators for ${symbol}/${conversionCurrency}`);

    if (!symbol || !conversionCurrency) {
      throw new BadRequestException(API_ERROR_MESSAGES.MISSING_REQUIRED_PARAMS);
    }

    return this.cryptoStatisticsService.getCryptoIndicators(symbol, conversionCurrency);
  }
}
