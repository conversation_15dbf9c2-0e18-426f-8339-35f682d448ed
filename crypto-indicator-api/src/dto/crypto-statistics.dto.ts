import { ApiProperty } from '@nestjs/swagger';

export class CryptocurrencyMappingDto {
  @ApiProperty({ example: 'ETH' })
  symbol!: string;

  @ApiProperty({ example: 'Ethereum' })
  name!: string;

  @ApiProperty({ example: 'ethereum' })
  slug!: string;

  @ApiProperty({ example: true })
  is_active!: boolean;

  @ApiProperty({ example: '2015-08-07T14:45:00' })
  first_historical_data!: string;

  @ApiProperty({ example: '2025-06-22T16:40:00' })
  last_historical_data!: string;

  @ApiProperty({ example: 1027 })
  id!: number;

  @ApiProperty({ example: 2 })
  rank!: number;
}

export class IndicatorValueDto {
  @ApiProperty() open!: number;
  @ApiProperty() high!: number;
  @ApiProperty() low!: number;
  @ApiProperty() close!: number;
  @ApiProperty() volume!: number;
  @ApiProperty() marketCap!: number;
  @ApiProperty() timestamp!: string;
  @ApiProperty() name!: string;
  @ApiProperty() hl2!: number;
  @ApiProperty() p1!: boolean;
  @ApiProperty() p2!: boolean;
  @ApiProperty() p3!: boolean;
  @ApiProperty({ enum: ['gold', 'blue', 'gray'] })
  color!: string;

  @ApiProperty() smma_15!: number;
  @ApiProperty() smma_19!: number;
  @ApiProperty() smma_25!: number;
  @ApiProperty() smma_29!: number;
}

export class CryptoCurrencyStatisticsDto {
  @ApiProperty({ example: 'ETH' })
  symbol!: string;

  @ApiProperty({ example: 'USD' })
  conversionCurrency!: string;

  @ApiProperty({ type: [IndicatorValueDto] })
  indicatorValues!: IndicatorValueDto[];

  @ApiProperty({ type: CryptocurrencyMappingDto })
  mapping!: CryptocurrencyMappingDto;
}
