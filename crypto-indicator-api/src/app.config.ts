import process from 'node:process';

const indicatorHost = process.env.INDICATOR_HOST;
const indicatorPort = process.env.INDICATOR_PORT;

if (indicatorHost === undefined || indicatorHost === null || indicatorHost.trim() === '') {
  throw new Error('INDICATOR_HOST environment variable is required');
}

if (indicatorPort === undefined || indicatorPort === null || indicatorPort.trim() === '') {
  throw new Error('INDICATOR_PORT environment variable is required');
}

export const AppConfig = {
  INDICATOR_HOST: indicatorHost,
  INDICATOR_PORT: indicatorPort,
};
