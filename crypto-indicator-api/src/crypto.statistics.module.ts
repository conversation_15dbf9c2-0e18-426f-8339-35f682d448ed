import { join } from 'node:path';

import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ServeStaticModule } from '@nestjs/serve-static';

import FinancialIndicatorRestClient from './api/financial.indicator.api';
import { CryptoStatisticsController } from './crypto.statistics.controller';
import { CryptoStatisticsService } from './crypto.statistics.service';
import { DataTransformationService } from './services/data-transformation.service';

@Module({
  imports: [
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', 'crypto-indicator-web', 'build'),
    }),
  ],
  controllers: [CryptoStatisticsController],
  providers: [
    CryptoStatisticsService,
    DataTransformationService,
    {
      provide: 'daemonClient',
      useClass: FinancialIndicatorRestClient,
    },
  ],
})
export class CryptoStatisticsModule {}
