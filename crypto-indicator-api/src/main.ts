import * as path from 'node:path';
import process from 'node:process';

import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as dotenv from 'dotenv';

import { CryptoStatisticsModule } from './crypto.statistics.module';

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '..', '..', '.env') });

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(CryptoStatisticsModule);

  const config = new DocumentBuilder()
    .setTitle('Crypto Indicator API')
    .setDescription('API for cryptocurrency technical indicators and statistics')
    .setVersion('1.0')
    .addTag('Crypto Statistics')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-docs', app, document);

  const DEFAULT_PORT = 6701;
  const port = Number(process.env.PORT) || DEFAULT_PORT;
  await app.listen(port);
}
void bootstrap();
