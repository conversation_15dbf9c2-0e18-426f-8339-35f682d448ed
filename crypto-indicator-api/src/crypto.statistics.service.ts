import { Inject, Injectable } from '@nestjs/common';

import {
  CryptoCurrencyData,
  DaemonClient,
} from './api/financial.indicator.api';
import { DataTransformationService } from './services/data-transformation.service';

@Injectable()
export class CryptoStatisticsService {
  constructor(
    @Inject('daemonClient')
    private readonly daemonClient: DaemonClient,
    private readonly transformationService: DataTransformationService,
  ) {}

  async getStructuredStatistics(): Promise<CryptoCurrencyData[]> {
    const daemonResponse = await this.daemonClient.getCryptoStatistics();
    return this.transformationService.transform(daemonResponse);
  }

  async getCryptoIndicators(
    symbol: string,
    conversionCurrency: string,
  ): Promise<CryptoCurrencyData> {
    const indicatorValues = await this.daemonClient.getCryptoIndicators(
      symbol,
      conversionCurrency,
    );

    // Return in consistent CryptoCurrencyData format
    // Note: mapping field is not available from the getCryptoIndicators endpoint
    // This method is used for chart data, mapping is only available from getStructuredStatistics
    return {
      symbol,
      conversionCurrency,
      indicatorValues,
      mapping: {
        symbol,
        name: symbol, // Fallback to symbol as name
        slug: symbol.toLowerCase(), // Fallback to lowercase symbol as slug
        is_active: true,
        first_historical_data: '',
        last_historical_data: '',
        id: 0,
        rank: 0,
      },
    };
  }
}
