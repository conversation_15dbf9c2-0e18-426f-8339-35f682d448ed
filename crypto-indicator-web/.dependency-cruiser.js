/** @type {import('dependency-cruiser').IConfiguration} */
module.exports = {
  forbidden: [
    // No circular dependencies
    {
      name: 'no-circular',
      severity: 'error',
      comment: 'Circular dependencies make code harder to understand and maintain',
      from: {},
      to: {
        circular: true
      }
    },
    
    // No orphan modules (except entry points)
    {
      name: 'no-orphans',
      severity: 'warn',
      comment: 'Orphan modules are likely dead code',
      from: {
        orphan: true,
        pathNot: [
          '^src/index\\.tsx$',
          '^src/App\\.tsx$',
          '\\.d\\.ts$',
          'test',
          'spec'
        ]
      },
      to: {}
    },
    
    // No dependencies on dev dependencies in production code
    {
      name: 'not-to-dev-dep',
      severity: 'error',
      comment: 'Production code should not depend on dev dependencies',
      from: {
        path: '^src',
        pathNot: [
          '\\.test\\.',
          '\\.spec\\.',
          '__tests__',
          '__mocks__'
        ]
      },
      to: {
        dependencyTypes: ['npm-dev']
      }
    },
    
    // No reaching into node_modules
    {
      name: 'not-to-unresolvable',
      severity: 'error',
      comment: 'Dependencies should be properly declared',
      from: {},
      to: {
        couldNotResolve: true
      }
    },
    
    // Enforce architectural boundaries
    {
      name: 'no-utils-to-components',
      severity: 'error',
      comment: 'Utility functions should not depend on UI components',
      from: {
        path: '^src/utils'
      },
      to: {
        path: '^src/components'
      }
    },
    
    {
      name: 'no-services-to-components',
      severity: 'error',
      comment: 'Services should not depend on UI components',
      from: {
        path: '^src/services'
      },
      to: {
        path: '^src/components'
      }
    },
    
    {
      name: 'no-hooks-to-components',
      severity: 'warn',
      comment: 'Hooks should generally not depend on specific components',
      from: {
        path: '^src/hooks'
      },
      to: {
        path: '^src/components'
      }
    }
  ],
  
  options: {
    doNotFollow: {
      path: [
        'node_modules',
        'build',
        'dist',
        'coverage',
        '\\.d\\.ts$'
      ]
    },
    
    includeOnly: '^src/',
    
    tsPreCompilationDeps: true,
    
    tsConfig: {
      fileName: 'tsconfig.json'
    },
    
    enhancedResolveOptions: {
      exportsFields: ['exports'],
      conditionNames: ['import', 'require', 'node', 'default']
    },
    
    reporterOptions: {
      dot: {
        collapsePattern: 'node_modules/[^/]+',
        theme: {
          graph: {
            bgcolor: 'transparent',
            splines: 'ortho',
            rankdir: 'TD'
          },
          modules: [
            {
              criteria: { source: '^src/components' },
              attributes: { fillcolor: '#ffcccc', style: 'filled' }
            },
            {
              criteria: { source: '^src/hooks' },
              attributes: { fillcolor: '#ccffcc', style: 'filled' }
            },
            {
              criteria: { source: '^src/services' },
              attributes: { fillcolor: '#ccccff', style: 'filled' }
            },
            {
              criteria: { source: '^src/utils' },
              attributes: { fillcolor: '#ffffcc', style: 'filled' }
            }
          ]
        }
      }
    }
  }
};
