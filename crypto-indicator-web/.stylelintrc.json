{"extends": ["stylelint-config-standard", "stylelint-config-recess-order"], "plugins": ["stylelint-order", "stylelint-declaration-strict-value", "stylelint-use-logical"], "rules": {"comment-empty-line-before": ["always", {"except": ["first-nested"], "ignore": ["after-comment", "stylelint-commands"]}], "custom-property-empty-line-before": ["always", {"except": ["after-custom-property", "first-nested"], "ignore": ["after-comment", "inside-single-line-block"]}], "custom-property-pattern": ["^(bg|text|accent|glass|border|neon|dark|shadow|spring|micro|blur|opacity|layout|spacing|font|animation|transition|z-index)-.+", {"message": "Expected custom property name to follow strict naming convention: --{category}-{name}"}], "keyframes-name-pattern": ["^[a-z][a-z0-9]*(-[a-z0-9]+)*$", {"message": "Expected keyframe name to be kebab-case"}], "declaration-empty-line-before": ["always", {"except": ["after-declaration", "first-nested"], "ignore": ["after-comment", "inside-single-line-block"]}], "rule-empty-line-before": ["always-multi-line", {"except": ["first-nested"], "ignore": ["after-comment"]}], "selector-class-pattern": ["^([a-z][a-z0-9]*)(-[a-z0-9]+)*(__[a-z0-9]+(-[a-z0-9]+)*)?(--[a-z0-9]+(-[a-z0-9]+)*)?$", {"message": "Expected class selector to follow BEM naming convention"}], "selector-id-pattern": ["^[a-z][a-z0-9]*(-[a-z0-9]+)*$", {"message": "Expected id selector to be kebab-case"}], "scale-unlimited/declaration-strict-value": [["background-color", "border-color", "outline-color", "text-decoration-color", "column-rule-color"], {"ignoreValues": ["currentColor", "inherit", "initial", "transparent", "unset", "#000", "#fff", "#ffffff", "#000000"], "message": "Use CSS custom properties for colors to maintain design system consistency"}], "unit-allowed-list": ["px", "rem", "em", "%", "vh", "vw", "vmin", "vmax", "dvh", "dvw", "svh", "svw", "deg", "rad", "grad", "turn", "ms", "s", "fr", "ch", "ex", "lh", "rlh", "dpi", "dppx", "dpcm", "x"], "color-hex-length": "short", "value-keyword-case": "lower", "function-name-case": "lower", "color-function-notation": "modern", "alpha-value-notation": "number", "hue-degree-notation": "angle", "import-notation": "string", "keyframe-selector-notation": "percentage-unless-within-keyword-only-block", "selector-not-notation": "complex", "selector-pseudo-element-colon-notation": "double", "csstools/use-logical": ["always", {"except": ["float", "clear", "resize", "text-align", "width", "height", "min-width", "min-height", "max-width", "max-height", "top", "right", "bottom", "left", "margin-top", "margin-right", "margin-bottom", "margin-left", "padding-top", "padding-right", "padding-bottom", "padding-left", "border-top", "border-right", "border-bottom", "border-left", "border-top-color", "border-right-color", "border-bottom-color", "border-left-color"]}], "no-duplicate-selectors": true, "no-empty-source": true, "block-no-empty": true, "declaration-block-no-duplicate-properties": [true, {"ignore": ["consecutive-duplicates-with-different-values"]}], "no-descending-specificity": null, "length-zero-no-unit": true, "shorthand-property-no-redundant-values": true, "declaration-block-no-redundant-longhand-properties": true, "font-weight-notation": "numeric", "selector-max-id": 0, "selector-max-universal": 1, "selector-max-type": 2, "selector-max-class": 3, "selector-max-combinators": 2, "selector-max-compound-selectors": 3, "selector-max-specificity": "0,3,2", "max-nesting-depth": 2, "declaration-block-single-line-max-declarations": 1, "no-unknown-animations": null, "property-no-vendor-prefix": [true, {"ignoreProperties": ["backdrop-filter", "appearance"]}], "value-no-vendor-prefix": [true, {"ignoreValues": ["box", "inline-box"]}], "declaration-property-value-no-unknown": true, "function-no-unknown": true, "function-url-no-scheme-relative": true, "function-url-scheme-allowed-list": ["https", "data"]}, "ignoreFiles": ["build/**/*", "dist/**/*", "node_modules/**/*"]}