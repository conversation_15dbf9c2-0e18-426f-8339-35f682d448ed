module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: './tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 2022,
  },
  plugins: [
    '@typescript-eslint',
    'react',
    'react-hooks',
    'jsx-a11y',
    'import',
    'unicorn',
    'sonarjs',
    'simple-import-sort',
  ],
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:@typescript-eslint/recommended-requiring-type-checking',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'plugin:unicorn/recommended',
    'react-app',
    'react-app/jest',
  ],
  env: {
    browser: true,
    es2022: true,
    jest: true,
    node: true,
  },
  ignorePatterns: [
    '.eslintrc.js',
    'build/',
    'node_modules/',
    'public/',
    'src/generated/**/*',
    '*.min.js',
    'scripts/generate-client.js',
  ],
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
        moduleDirectory: ['node_modules', 'src'],
      },
    },
    'import/parsers': {
      '@typescript-eslint/parser': ['.ts', '.tsx'],
    },
  },
  rules: {
    // ===== TYPESCRIPT ESLINT RULES - STRICT TYPE SAFETY FOR AI AGENTS =====
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_',
        destructuredArrayIgnorePattern: '^_',
        ignoreRestSiblings: true,
      },
    ],
    '@typescript-eslint/prefer-nullish-coalescing': 'error',
    '@typescript-eslint/prefer-optional-chain': 'error',
    '@typescript-eslint/no-floating-promises': 'error',
    '@typescript-eslint/await-thenable': 'error',
    '@typescript-eslint/no-misused-promises': [
      'error',
      {
        checksVoidReturn: false,
        checksConditionals: true,
        checksSpreads: true,
      },
    ],
    '@typescript-eslint/require-await': 'error',
    '@typescript-eslint/no-unnecessary-type-assertion': 'error',
    '@typescript-eslint/prefer-as-const': 'error',
    '@typescript-eslint/switch-exhaustiveness-check': 'error',
    '@typescript-eslint/no-unsafe-assignment': 'error',
    '@typescript-eslint/no-unsafe-call': 'error',
    '@typescript-eslint/no-unsafe-member-access': 'error',
    '@typescript-eslint/no-unsafe-return': 'error',
    '@typescript-eslint/no-unsafe-argument': 'error',
    '@typescript-eslint/strict-boolean-expressions': 'error',
    '@typescript-eslint/no-non-null-assertion': 'error',
    '@typescript-eslint/prefer-readonly': 'error',
    '@typescript-eslint/prefer-readonly-parameter-types': 'off', // Too strict for React
    '@typescript-eslint/no-confusing-void-expression': 'error',

    // ===== REACT RULES - PREVENT COMMON AI MISTAKES =====
    'react/react-in-jsx-scope': 'off', // Not needed with React 17+
    'react/prop-types': 'off', // Using TypeScript for prop validation
    'react/display-name': 'error',
    'react/no-array-index-key': 'warn',
    'react/no-danger': 'error',
    'react/no-danger-with-children': 'error',
    'react/jsx-key': ['error', { checkFragmentShorthand: true }],
    'react/jsx-no-duplicate-props': 'error',
    'react/jsx-no-undef': 'error',
    'react/jsx-uses-react': 'off', // Not needed with React 17+
    'react/jsx-uses-vars': 'error',
    'react/no-deprecated': 'error',
    'react/no-direct-mutation-state': 'error',
    'react/no-is-mounted': 'error',
    'react/no-render-return-value': 'error',
    'react/no-string-refs': 'error',
    'react/no-unescaped-entities': 'error',
    'react/no-unknown-property': 'error',
    'react/no-unsafe': 'error',
    'react/require-render-return': 'error',
    'react/self-closing-comp': 'error',
    'react/void-dom-elements-no-children': 'error',
    'react/jsx-no-target-blank': ['error', { enforceDynamicLinks: 'always' }],
    'react/jsx-no-script-url': 'error',
    'react/jsx-pascal-case': 'error',
    'react/jsx-boolean-value': ['error', 'never'],
    'react/jsx-curly-brace-presence': [
      'error',
      { props: 'never', children: 'never' },
    ],
    'react/no-unstable-nested-components': 'error',

    // ===== REACT HOOKS RULES =====
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'error', // Stricter for AI agents

    // ===== ACCESSIBILITY RULES - CRITICAL FOR TRADING INTERFACES =====
    'jsx-a11y/alt-text': 'error',
    'jsx-a11y/anchor-has-content': 'error',
    'jsx-a11y/anchor-is-valid': 'error',
    'jsx-a11y/click-events-have-key-events': 'error',
    'jsx-a11y/interactive-supports-focus': 'error',
    'jsx-a11y/no-noninteractive-element-interactions': 'error',
    'jsx-a11y/role-has-required-aria-props': 'error',
    'jsx-a11y/role-supports-aria-props': 'error',
    'jsx-a11y/no-autofocus': 'warn', // Can be problematic for trading interfaces

    // ===== IMPORT ORGANIZATION - PREVENT AI IMPORT CHAOS =====
    'simple-import-sort/imports': [
      'error',
      {
        groups: [
          // React first
          ['^react$', '^react/'],
          // External packages
          ['^@?\\w'],
          // Internal packages
          ['^@/'],
          // Parent imports
          ['^\\.\\.(?!/?$)', '^\\.\\./?$'],
          // Other relative imports
          ['^\\./(?=.*/)(?!/?$)', '^\\.(?!/?$)', '^\\./?$'],
          // Type imports
          ['^.+\\u0000$'],
        ],
      },
    ],
    'import/no-unresolved': 'error',
    'import/no-cycle': 'error',
    'import/no-self-import': 'error',
    'import/no-useless-path-segments': 'error',
    'import/no-duplicates': 'error',
    'import/first': 'error',
    'import/newline-after-import': 'error',
    'import/no-default-export': 'off', // React components often use default exports

    // ===== CODE COMPLEXITY LIMITS - PREVENT AI OVER-ENGINEERING =====
    complexity: ['error', { max: 12 }], // Stricter for AI agents
    'max-depth': ['error', { max: 4 }],
    'max-lines': [
      'error',
      { max: 250, skipBlankLines: true, skipComments: true },
    ],
    'max-lines-per-function': [
      'error',
      { max: 60, skipBlankLines: true, skipComments: true },
    ],
    'max-params': ['error', { max: 4 }], // Stricter for AI agents
    'max-statements': ['error', { max: 20 }],
    'max-nested-callbacks': ['error', { max: 3 }],

    // ===== SECURITY RULES - PREVENT AI SECURITY VULNERABILITIES =====
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    'no-caller': 'error',
    'no-extend-native': 'error',
    'no-global-assign': 'error',

    // ===== PERFORMANCE AND BEST PRACTICES =====
    'prefer-const': 'error',
    'no-var': 'error',
    'prefer-arrow-callback': 'error',
    'prefer-template': 'error',
    'no-nested-ternary': 'error',
    'no-unneeded-ternary': 'error',
    'no-else-return': 'error',
    'consistent-return': 'off', // React components don't always need consistent returns
    'no-return-assign': 'error',
    'no-console': 'warn',
    'no-debugger': 'error',
    'no-alert': 'error',
    eqeqeq: ['error', 'always'],
    curly: ['error', 'all'],

    // Allow void for fire-and-forget promises
    'no-void': ['error', { allowAsStatement: true }],

    // Allow magic numbers for common React values
    'no-magic-numbers': [
      'warn',
      {
        ignore: [-1, 0, 1, 2, 100, 1000],
        ignoreArrayIndexes: true,
        ignoreDefaultValues: true,
        ignoreClassFieldInitialValues: true,
      },
    ],

    // Disable some opinionated rules for React
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',

    'no-duplicate-imports': 'off', // Handled by simple-import-sort

    // ===== UNICORN RULES - ADVANCED CODE QUALITY FOR AI AGENTS =====
    'unicorn/filename-case': [
      'error',
      { case: 'kebabCase', ignore: ['\\.tsx?$'] },
    ],
    'unicorn/no-null': 'off', // React often uses null
    'unicorn/prevent-abbreviations': 'off', // Too strict for React props
    'unicorn/no-array-reduce': 'off', // Reduce is useful in many cases
    'unicorn/no-array-for-each': 'off', // forEach is fine for side effects
    'unicorn/prefer-module': 'off', // CommonJS is still used
    'unicorn/prefer-node-protocol': 'off', // Not applicable for browser code
    'unicorn/no-process-exit': 'off', // Not applicable for browser code
    'unicorn/import-style': 'off', // Conflicts with React conventions
    'unicorn/prefer-top-level-await': 'off', // Not always applicable
    'unicorn/no-anonymous-default-export': 'off', // React components often use anonymous defaults
    'unicorn/prefer-ternary': 'error',
    'unicorn/prefer-logical-operator-over-ternary': 'error',
    'unicorn/no-nested-ternary': 'error',
    'unicorn/better-regex': 'error',
    'unicorn/consistent-destructuring': 'error',
    'unicorn/no-useless-undefined': 'error',
    'unicorn/prefer-includes': 'error',
    'unicorn/prefer-string-starts-ends-with': 'error',

    // ===== SONARJS RULES - COMPLEXITY AND CODE SMELL DETECTION =====
    'sonarjs/cognitive-complexity': ['error', 15],
    'sonarjs/no-duplicate-string': ['error', { threshold: 3 }],
    'sonarjs/no-identical-functions': 'error',
    'sonarjs/no-redundant-boolean': 'error',
    'sonarjs/no-unused-collection': 'error',
    'sonarjs/prefer-immediate-return': 'error',
    'sonarjs/prefer-object-literal': 'error',
    'sonarjs/prefer-single-boolean-return': 'error',
    'sonarjs/no-small-switch': 'error',
    'sonarjs/no-redundant-jump': 'error',

    // ===== ERROR PREVENTION FOR AI AGENTS =====
    'no-implicit-coercion': 'error',
    'no-implicit-globals': 'error',
    'no-invalid-this': 'error',
    'no-lone-blocks': 'error',
    'no-loop-func': 'error',
    'no-multi-assign': 'error',
    'no-new': 'error',
    'no-new-wrappers': 'error',
    'no-param-reassign': 'error',
    'no-proto': 'error',
    'no-redeclare': 'error',
    'no-self-compare': 'error',
    'no-sequences': 'error',
    'no-throw-literal': 'error',
    'no-unused-expressions': 'error',
    'no-useless-call': 'error',
    'no-useless-concat': 'error',
    'no-useless-return': 'error',
    'no-warning-comments': 'warn',
    radix: 'error',
    yoda: 'error',

    // Performance optimizations for React
    'prefer-destructuring': [
      'error',
      {
        array: true,
        object: true,
      },
      {
        enforceForRenamedProperties: false,
      },
    ],
  },
  overrides: [
    {
      // Relax some rules for test files
      files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-unsafe-assignment': 'off',
        '@typescript-eslint/no-unsafe-member-access': 'off',
        '@typescript-eslint/no-unsafe-call': 'off',
        'no-magic-numbers': 'off',
        'max-lines-per-function': 'off',
        'max-statements': 'off',
        complexity: 'off',
        'react/display-name': 'off',
        'sonarjs/no-duplicate-string': 'off',
        'sonarjs/cognitive-complexity': 'off',
        'unicorn/consistent-function-scoping': 'off',
      },
    },
    {
      // Relax rules for generated code
      files: ['src/generated/**/*'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-unused-vars': 'off',
        '@typescript-eslint/no-unsafe-assignment': 'off',
        '@typescript-eslint/no-unsafe-member-access': 'off',
        '@typescript-eslint/no-unsafe-call': 'off',
        'unicorn/filename-case': 'off',
        'sonarjs/cognitive-complexity': 'off',
        complexity: 'off',
        'max-lines': 'off',
        'max-lines-per-function': 'off',
      },
    },
    {
      // Relax some rules for configuration files
      files: [
        '*.config.js',
        '*.config.ts',
        'scripts/**/*',
        'vite.config.*',
        'webpack.config.*',
      ],
      rules: {
        'no-console': 'off',
        '@typescript-eslint/no-var-requires': 'off',
        'import/no-extraneous-dependencies': 'off',
        'unicorn/prefer-module': 'off',
        'sonarjs/cognitive-complexity': 'off',
      },
    },
  ],
};
