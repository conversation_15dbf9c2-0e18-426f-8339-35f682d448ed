/**
 * Loading and Error State Styles
 * 
 * Styles for loading states, error boundaries, and utility state components.
 * 
 * Dependencies: variables.css, base.css
 */

.loading-container {
  max-width: 600px;
  padding: 60px 40px;
  margin: 40px auto;
  color: var(--text-primary);
  text-align: center;
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
}

/* Loading State Styles */
.loading-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 15px;
  border: 3px solid rgb(255 255 255 / 0.1);
  border-top: 3px solid var(--accent-cyan);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgb(0 0 0 / 0.3);
  animation: spin 1s linear infinite;
}

.loading-message {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary);
}

.loading-small .loading-spinner {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-large .loading-spinner {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

/* Error State Styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.error-icon {
  margin-bottom: 15px;
  font-size: 48px;
  color: var(--accent-red, #ef4444);
}

.error-message {
  max-width: 400px;
  margin: 0 0 20px;
  font-size: 16px;
  font-weight: 500;
  color: #ef4444;
}

.retry-button {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: translateY(-1px);
}

/* Error Boundary Styles */
.error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  margin: 20px;
  text-align: center;
  background: var(--glass-bg-strong);
  border: 1px solid var(--neon-pink);
  border-radius: 16px;
  box-shadow: var(--glass-shadow), 0 0 20px rgb(243 139 168 / 0.3);
  backdrop-filter: var(--glass-blur);
}

.error-boundary h3 {
  margin: 0 0 10px;
  font-weight: 600;
  color: var(--neon-pink);
  text-shadow: 0 0 10px rgb(255 0 255 / 0.5);
}

.error-boundary p {
  margin: 0 0 20px;
  color: var(--text-secondary);
}
