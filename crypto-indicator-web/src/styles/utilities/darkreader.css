/**
 * DarkReader Controls Styles
 * 
 * Styles for the DarkReader integration controls including toggles,
 * sliders, presets, and responsive behavior.
 * 
 * Dependencies: variables.css, base.css
 */

/* DarkReader Controls Styling */
.darkreader-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  padding: 12px;
  font-size: 12px;
  color: var(--text-primary);
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
}

.darkreader-main-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.darkreader-toggle {
  padding: 6px 12px;
  font-size: 11px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.darkreader-toggle:hover {
  background: var(--glass-bg-strong);
  border-color: var(--accent-teal);
}

.darkreader-toggle.enabled {
  color: var(--accent-teal);
  background: rgb(148 226 213 / 0.2);
  border-color: var(--accent-teal);
}

.darkreader-settings-toggle {
  padding: 6px 8px;
  font-size: 11px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.darkreader-settings-toggle:hover {
  background: var(--glass-bg-strong);
  border-color: var(--accent-blue);
}

.darkreader-advanced-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  padding-top: 12px;
  margin-top: 12px;
  border-top: 1px solid var(--glass-border);
}

.darkreader-control-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.darkreader-control-group label {
  font-size: 10px;
  font-weight: 500;
  color: var(--text-secondary);
}

.darkreader-slider {
  width: 100%;
  height: 4px;
  appearance: none;
  outline: none;
  background: var(--bg-surface);
  border-radius: 2px;
}

.darkreader-slider::-webkit-slider-thumb {
  width: 12px;
  height: 12px;
  appearance: none;
  cursor: pointer;
  background: var(--accent-teal);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgb(0 0 0 / 0.3);
}

.darkreader-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  cursor: pointer;
  background: var(--accent-teal);
  border: none;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgb(0 0 0 / 0.3);
}

.darkreader-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.darkreader-preset {
  flex: 1;
  min-width: 0;
  padding: 4px 8px;
  font-size: 9px;
  color: var(--text-secondary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.darkreader-preset:hover {
  color: var(--accent-purple);
  background: var(--glass-bg-strong);
  border-color: var(--accent-purple);
}

/* Mobile DarkReader controls optimization */
@media (width <= 768px) {
  .darkreader-controls {
    top: 10px;
    right: 10px;
    padding: 8px;
    font-size: 11px;
  }

  .darkreader-toggle, .darkreader-settings-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: 8px 12px;
    font-size: 12px;
  }

  .darkreader-advanced-controls {
    min-width: 160px;
  }

  .darkreader-slider::-webkit-slider-thumb {
    width: 16px;
    height: 16px;
  }

  .darkreader-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
  }

  .darkreader-preset {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 10px;
  }
}

@media (width <= 480px) {
  .darkreader-controls {
    top: 5px;
    right: 5px;
    padding: 6px;
    font-size: 10px;
  }

  .darkreader-toggle, .darkreader-settings-toggle {
    min-width: 48px;
    min-height: 48px;
    padding: 10px 14px;
    font-size: 11px;
  }

  .darkreader-advanced-controls {
    min-width: 140px;
  }
}
