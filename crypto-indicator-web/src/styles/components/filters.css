/**
 * Table Filters Component Styles
 * 
 * Styles for the TableFilters component including filter controls,
 * inputs, selects, and responsive behavior.
 * 
 * Dependencies: variables.css, base.css
 */

/* Table Filters Styles */
.table-filters {
  padding: 16px;
  margin: 16px 0;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
  margin-bottom: 12px;
}

.filters-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

.filter-group label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-input,
.filter-select {
  padding: 6px 8px;
  font-size: 13px;
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 2px rgb(137 180 250 / 0.2);
}

.range-group {
  min-width: 200px;
}

.range-inputs {
  display: flex;
  gap: 8px;
  align-items: center;
}

.range-input {
  flex: 1;
  min-width: 80px;
}

.range-separator {
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-actions {
  display: flex;
  align-items: flex-end;
  margin-left: auto;
}

.clear-filters-btn {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  cursor: pointer;
  background: var(--accent-red);
  border: none;
  border-radius: 4px;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.clear-filters-btn:hover {
  background: #e74c3c;
  transform: translateY(-1px);
}

.filter-results {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  border-top: 1px solid var(--border-primary);
}

.results-count {
  font-weight: 500;
}

.filtered-indicator {
  font-weight: 600;
  color: var(--accent-blue);
}

/* Mobile Responsive Styles for Filters */
@media (width <= 768px) {
  .table-filters {
    padding: 12px;
  }

  .filters-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
    min-width: auto;
  }

  .range-group {
    min-width: auto;
  }

  .range-inputs {
    flex-direction: column;
    gap: 8px;
  }

  .range-input {
    min-width: auto;
  }

  .filter-actions {
    align-items: stretch;
    margin-left: 0;
  }

  .clear-filters-btn {
    width: 100%;
    padding: 8px 12px;
  }

  .filter-results {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  /* Sortable headers mobile adjustments */
  .sortable-header-content {
    gap: 4px;
  }

  .sort-icon {
    font-size: 10px;
  }
}
