/**
 * Game Component Styles
 * 
 * Styles for Signal Prediction Challenge game components including
 * prediction overlay, game stats, toggle button, and results display.
 * 
 * Dependencies: variables.css, base.css
 */

/* Game Toggle */
.game-toggle-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.game-toggle {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  cursor: pointer;
}

.game-toggle:hover {
  border-color: var(--accent-teal);
  box-shadow: var(--shadow-hover);
  transform: translateY(-1px);
}

.game-toggle.active {
  background: rgb(94 226 213 / 0.2);
  border-color: var(--accent-teal);
  box-shadow: 0 0 20px rgb(94 226 213 / 0.3);
}

.toggle-icon {
  font-size: 16px;
}

.game-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  background: var(--accent-teal);
  border: 2px solid var(--bg-primary);
  border-radius: 50%;
  box-shadow: 0 0 8px var(--accent-teal);
}

.indicator-dot {
  width: 100%;
  height: 100%;
  background: var(--accent-teal);
  border-radius: 50%;
  animation: pulse-glow 2s ease-in-out infinite;
}

.game-status {
  font-size: 11px;
  color: var(--text-muted);
  text-align: center;
}

/* Game Stats */
.game-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px 16px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
}

.stats-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-width: 60px;
}

.stat-label {
  font-size: 10px;
  font-weight: 500;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-item.score .stat-value {
  color: var(--accent-yellow);
}

.stat-item.timer .stat-value {
  color: var(--accent-teal);
}

.timer-value {
  font-family: 'Courier New', monospace;
}

.streak-indicator {
  padding: 4px 8px;
  font-size: 12px;
  color: var(--accent-yellow);
  background: rgb(249 226 175 / 0.1);
  border: 1px solid rgb(249 226 175 / 0.3);
  border-radius: 8px;
  text-align: center;
}

/* Mobile Game Stats */
.game-stats-mobile {
  padding: 8px 12px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  backdrop-filter: var(--glass-blur);
}

.mobile-stats-row {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.mobile-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.mobile-stat-value {
  font-size: 14px;
  font-weight: 700;
  color: var(--text-primary);
}

.mobile-stat-label {
  font-size: 9px;
  color: var(--text-muted);
  text-transform: uppercase;
}

.mobile-timer {
  display: flex;
  justify-content: center;
  gap: 4px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--border-primary);
}

.timer-label {
  font-size: 12px;
  color: var(--text-muted);
}

.timer-value {
  font-size: 12px;
  font-weight: 700;
  color: var(--accent-teal);
}

/* Prediction Overlay */
.prediction-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.prediction-backdrop {
  position: absolute;
  inset: 0;
  background: rgb(0 0 0 / 0.8);
  backdrop-filter: blur(8px);
}

.prediction-content {
  position: relative;
  z-index: 1001;
  width: 100%;
  max-width: 500px;
  padding: 24px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  backdrop-filter: var(--glass-blur-strong);
  box-shadow: var(--shadow-elevated);
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.prediction-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.prediction-timer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  font-size: 18px;
  font-weight: 700;
  border: 3px solid currentcolor;
  border-radius: 50%;
  background: rgb(0 0 0 / 0.3);
}

.timer-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.prediction-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.prediction-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--glass-bg);
  border: 2px solid var(--button-color, var(--border-primary));
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  cursor: pointer;
}

.prediction-button:hover {
  background: rgb(255 255 255 / 0.1);
  box-shadow: 0 0 20px var(--button-color, var(--border-primary));
  transform: translateY(-2px);
}

.prediction-icon {
  font-size: 20px;
  font-weight: 700;
  color: var(--button-color, var(--text-primary));
}

.prediction-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
  text-align: left;
}

.prediction-description {
  font-size: 12px;
  color: var(--text-muted);
}

.prediction-progress {
  position: relative;
  height: 4px;
  margin-bottom: 16px;
  background: var(--border-primary);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.1s linear;
}

.prediction-hint {
  text-align: center;
}

.prediction-hint p {
  margin: 0;
  font-size: 14px;
  color: var(--text-muted);
}

/* Prediction Result */
.prediction-result {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 1002;
  transform: translate(-50%, -50%);
  padding: 24px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  backdrop-filter: var(--glass-blur-strong);
  box-shadow: var(--shadow-elevated);
}

.result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.result-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  font-size: 32px;
  font-weight: 700;
  border-radius: 50%;
}

.result-status.correct {
  color: var(--accent-green);
  background: rgb(166 227 161 / 0.2);
  border: 2px solid var(--accent-green);
}

.result-status.incorrect {
  color: var(--accent-red);
  background: rgb(243 139 168 / 0.2);
  border: 2px solid var(--accent-red);
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-prediction,
.result-actual {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  font-size: 14px;
}

.result-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.points-earned {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-yellow);
}

.time-taken {
  font-size: 12px;
  color: var(--text-muted);
}

/* Game Summary */
.game-summary-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: rgb(0 0 0 / 0.8);
  backdrop-filter: blur(8px);
}

.game-summary {
  width: 100%;
  max-width: 600px;
  padding: 32px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  backdrop-filter: var(--glass-blur-strong);
  box-shadow: var(--shadow-elevated);
}

.summary-header {
  text-align: center;
  margin-bottom: 24px;
}

.summary-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  color: var(--text-primary);
}

.new-best-badge {
  display: inline-block;
  padding: 4px 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-yellow);
  background: rgb(249 226 175 / 0.2);
  border: 1px solid var(--accent-yellow);
  border-radius: 20px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.summary-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 16px;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
}

.summary-stat-label {
  font-size: 12px;
  color: var(--text-muted);
  text-transform: uppercase;
}

.summary-stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
}

.summary-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.summary-button {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border: 2px solid;
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  cursor: pointer;
}

.summary-button.play-again {
  color: var(--accent-teal);
  background: rgb(94 226 213 / 0.1);
  border-color: var(--accent-teal);
}

.summary-button.play-again:hover {
  background: rgb(94 226 213 / 0.2);
  box-shadow: 0 0 20px rgb(94 226 213 / 0.3);
}

.summary-button.close {
  color: var(--text-muted);
  background: var(--glass-bg);
  border-color: var(--border-primary);
}

.summary-button.close:hover {
  color: var(--text-primary);
  border-color: var(--border-hover);
}

/* Animations */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .prediction-content {
    padding: 20px;
  }
  
  .prediction-title {
    font-size: 16px;
  }
  
  .prediction-timer {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
  
  .prediction-button {
    padding: 12px 16px;
  }
  
  .game-summary {
    padding: 24px;
  }
  
  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .summary-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .stats-container {
    gap: 12px;
  }
  
  .stat-item {
    min-width: 50px;
  }
  
  .stat-value {
    font-size: 14px;
  }
  
  .prediction-buttons {
    gap: 8px;
  }
  
  .prediction-button {
    padding: 10px 12px;
  }
}
