/**
 * Signal Badge Component Styles
 * 
 * Styles for the SignalBadge component including different signal states,
 * interactive behaviors, tooltips, and accessibility features.
 * 
 * Dependencies: variables.css, base.css
 */

.signal-badge {
  position: relative;
  box-sizing: border-box;
  display: inline-flex;
  gap: 6px;
  align-items: center;
  min-height: 32px;
  padding: 8px 16px;
  overflow: visible;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  text-shadow: 0 0 8px currentcolor;
  border: 1px solid transparent;
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

/* Custom fast tooltip for signal badges */
.signal-badge-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  z-index: 1000;
  padding: 8px 12px;
  margin-bottom: 8px;
  font-size: 11px;
  font-weight: 400;
  color: var(--text-primary);
  text-transform: none;
  white-space: nowrap;
  pointer-events: none;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-hover);
  opacity: 0;
  transform: translateX(-50%);
  transform: translateX(-50%) translateY(5px);
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
}

.signal-badge-tooltip.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.signal-badge-tooltip::after {
  position: absolute;
  top: 100%;
  left: 50%;
  content: '';
  border: 5px solid transparent;
  border-top-color: var(--border-primary);
  transform: translateX(-50%);
}

.signal-badge::before {
  position: absolute;
  inset: 0;
  z-index: -1;
  content: '';
  background: inherit;
  opacity: 0.1;
}

/* Signal Badge States */
.signal-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  font-size: 14px;
  font-weight: 700;
}

.signal-label {
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.signal-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  font-size: 14px;
  animation: spin 1s linear infinite;
}

/* Interactive States */
.clickable-signal {
  cursor: pointer;
  border-width: 2px;
  box-shadow: var(--shadow-subtle);
  transition: all 0.3s ease;
}

.clickable-signal:hover {
  border-color: var(--accent-blue);
  box-shadow: var(--shadow-hover);
  backdrop-filter: var(--glass-blur-strong);
  transform: translateY(-2px) scale(1.05);
}

.clickable-signal:focus {
  outline: none;
  box-shadow: var(--shadow-hover), 0 0 0 3px rgb(255 255 255 / 0.2);
  transform: translateY(-1px);
}

.clickable-signal:active {
  box-shadow: var(--shadow-subtle);
  transform: translateY(0) scale(1.02);
}

/* Loading and Disabled States */
.signal-loading {
  pointer-events: none;
  cursor: wait;
  opacity: 0.7;
}

.signal-disabled {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
  filter: grayscale(0.3);
}

/* Signal Color Variants - Semantic Trading Colors */
.signal-gold {
  color: var(--accent-yellow);
  background: rgb(249 226 175 / 0.2);
  border-color: rgb(249 226 175 / 0.4);
  box-shadow: var(--shadow-subtle), 0 0 12px rgb(249 226 175 / 0.3);
}

.signal-gold.clickable-signal {
  background: rgb(249 226 175 / 0.25);
  border-color: var(--accent-yellow);
}

.signal-gold.clickable-signal:hover {
  background: rgb(249 226 175 / 0.35);
  box-shadow: var(--shadow-hover), 0 0 20px rgb(249 226 175 / 0.5);
}

.signal-blue {
  color: var(--accent-blue);
  background: rgb(137 180 250 / 0.2);
  border-color: rgb(137 180 250 / 0.4);
  box-shadow: var(--shadow-subtle), 0 0 12px rgb(137 180 250 / 0.3);
}

.signal-blue.clickable-signal {
  background: rgb(137 180 250 / 0.25);
  border-color: var(--accent-blue);
}

.signal-blue.clickable-signal:hover {
  background: rgb(137 180 250 / 0.35);
  box-shadow: var(--shadow-hover), 0 0 20px rgb(137 180 250 / 0.5);
}

.signal-gray {
  color: var(--text-muted);
  background: rgb(147 153 178 / 0.2);
  border-color: rgb(147 153 178 / 0.4);
  box-shadow: var(--shadow-subtle), 0 0 12px rgb(147 153 178 / 0.2);
}

.signal-gray.clickable-signal {
  background: rgb(147 153 178 / 0.25);
  border-color: var(--text-muted);
}

.signal-gray.clickable-signal:hover {
  background: rgb(147 153 178 / 0.35);
  box-shadow: var(--shadow-hover), 0 0 20px rgb(147 153 178 / 0.4);
}

.signal-default {
  color: var(--text-muted);
  background: rgb(147 153 178 / 0.15);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-subtle);
}
