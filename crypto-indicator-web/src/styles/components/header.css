/**
 * Header Component Styles
 * 
 * Styles for the DashboardHeader component including title, subtitle,
 * lightning icon, and live indicator.
 * 
 * Dependencies: variables.css, base.css
 */

.header {
  position: relative;
  padding: 20px 24px;
  margin-bottom: 30px;
  overflow: hidden;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.header:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-hover);
}

.header::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  display: flex;
  gap: 12px;
  align-items: center;
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.lightning-icon {
  font-size: 2.4rem;
  color: var(--accent-yellow);
  filter: drop-shadow(0 2px 4px rgb(0 0 0 / 0.3));
  transition: all 0.3s ease;
}

.lightning-icon:hover {
  filter: drop-shadow(0 2px 6px rgb(0 0 0 / 0.4));
  transform: scale(1.05);
}

.title-text {
  text-shadow: 0 2px 8px rgb(0 0 0 / 0.4);
}

.header-status {
  display: flex;
  align-items: center;
}

.live-indicator {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 4px 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--accent-green);
  background: rgb(166 227 161 / 0.1);
  border: 1px solid var(--accent-green);
  border-radius: 20px;
}

.live-dot {
  width: 6px;
  height: 6px;
  background: var(--accent-green);
  border-radius: 50%;
  animation: live-pulse 2s ease-in-out infinite;
}

.live-text {
  letter-spacing: 0.5px;
}

.header-subtitle {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-secondary);
  opacity: 0.9;
}
