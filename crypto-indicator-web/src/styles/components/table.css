/**
 * Table Component Styles
 * 
 * Styles for the StatisticsTable component including table container,
 * table structure, sticky columns, sortable headers, and clickable symbols.
 * 
 * Dependencies: variables.css, base.css
 */

.table-container {
  position: relative;
  overflow: auto visible;
  background: var(--glass-bg-strong);
  border: 1px solid var(--border-primary);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  -webkit-overflow-scrolling: touch;
}

.table-container:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-hover);
}

.table-container::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-purple), transparent);
  opacity: 0.3;
}

.stats-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  color: var(--text-primary);
  background: rgb(148 226 213 / 0.03);
  border-bottom: 1px solid var(--border-primary);
}

.stats-info button {
  padding: 10px 20px;
  margin-left: 15px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.stats-info button:hover:not(:disabled) {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: translateY(-1px);
}

.stats-info button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.table {
  position: relative;
  width: 100%;
  min-width: 700px;
  font-size: 14px;
  table-layout: auto;
  border-collapse: collapse;
  background: transparent;
}

/* Sticky first column for all screen sizes */
.table th:first-child,
.table td:first-child {
  position: sticky;
  left: 0;
  z-index: 10;
  min-width: 120px;
  background: var(--bg-primary);
  border-right: 2px solid var(--border-primary);
  box-shadow: 2px 0 4px rgb(0 0 0 / 0.1);
}

.table th:first-child {
  z-index: 11;
  font-weight: 600;
  background: var(--bg-surface);
}

/* Ensure sticky column maintains row hover effects with solid backgrounds */
.table tr:hover td:first-child {
  background: var(--bg-elevated);
}

.table tr:nth-child(even) td:first-child {
  background: var(--bg-secondary);
}

.table th {
  position: relative;
  padding: 15px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgb(255 255 255 / 0.3);
  background: rgb(0 255 255 / 0.05);
  border-bottom: 1px solid rgb(255 255 255 / 0.1);
}

.table td {
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid rgb(255 255 255 / 0.1);
}

.table tr:hover {
  background: rgb(148 226 213 / 0.05);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.table tr:nth-child(even) {
  background: rgb(255 255 255 / 0.01);
}

/* Clickable Symbol Styles */
.clickable-symbol {
  position: relative;
  display: inline-block;
  padding: 6px 10px;
  font-weight: 600;
  text-decoration: none;
  text-decoration: underline;
  text-decoration-color: transparent;
  text-underline-offset: 3px;
  cursor: pointer;
  background: rgb(137 180 250 / 0.05);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.clickable-symbol::before {
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 10px;
  content: '🔗';
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.clickable-symbol::after {
  position: absolute;
  bottom: 2px;
  left: 50%;
  width: 0;
  height: 1px;
  content: '';
  background: var(--accent-blue);
  transform: translateX(-50%);
  transition: all 0.2s ease;
}

.clickable-symbol:hover {
  color: var(--accent-blue);
  text-decoration-color: var(--accent-blue);
  background: rgb(137 180 250 / 0.15);
  border-color: var(--accent-blue);
  box-shadow: 0 4px 12px rgb(137 180 250 / 0.3);
  transform: translateY(-2px);
}

.clickable-symbol:hover::before {
  opacity: 1;
}

.clickable-symbol:hover::after {
  width: 80%;
}

.clickable-symbol:active {
  box-shadow: 0 2px 6px rgb(137 180 250 / 0.4);
  transform: translateY(-1px);
}

/* Sortable Table Header Styles */
.sortable-header {
  position: relative;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.sortable-header:hover {
  background: var(--bg-elevated);
}

.sortable-header.active {
  background: var(--bg-secondary);
}

.sortable-header-content {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-text {
  flex: 1;
}

.sort-icon {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.sortable-header:hover .sort-icon,
.sortable-header.active .sort-icon {
  color: var(--accent-blue);
  opacity: 1;
}

/* Table Icons and Badges */
.rank-badge {
  display: inline-block;
  min-width: 30px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: var(--accent-teal);
  background: var(--glass-bg-strong);
  border: 1px solid var(--accent-teal);
  border-radius: 12px;
  box-shadow: var(--shadow-subtle);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.rank-badge:hover {
  box-shadow: var(--shadow-hover);
  transform: scale(1.05);
}

.crypto-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 10px;
  font-size: 12px;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  border: 1px solid var(--border-primary);
  border-radius: 50%;
  box-shadow: var(--shadow-subtle);
  transition: all 0.3s ease;
}

.crypto-icon:hover {
  box-shadow: var(--shadow-hover);
  transform: scale(1.05);
}

.symbol-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: var(--text-primary);
}
