/**
 * CSS Custom Properties and Design Tokens
 * 
 * This file contains all CSS custom properties (variables) used throughout
 * the crypto indicator application. These variables define the design system
 * including colors, spacing, shadows, and animation properties.
 */

/* CSS Custom Properties for Comfortable Dark Theme */
:root {
  /* Comfortable Dark Base Colors (inspired by Catppuccin/Night Owl) */
  --bg-primary: #1e1e2e;
  --bg-secondary: #181825;
  --bg-surface: #313244;
  --bg-elevated: #45475a;

  /* Subtle Glass Effects */
  --glass-bg: rgb(255 255 255 / 0.05);
  --glass-bg-strong: rgb(255 255 255 / 0.08);
  --glass-border: rgb(255 255 255 / 0.15);
  --glass-shadow: 0 4px 16px rgb(0 0 0 / 0.4);
  --glass-blur: blur(12px);
  --glass-blur-strong: blur(16px);

  /* Comfortable Background Gradients */
  --dark-bg-primary: var(--bg-primary);
  --dark-bg-secondary: var(--bg-secondary);
  --dark-bg-gradient: linear-gradient(135deg, #1e1e2e 0%, #181825 50%, #313244 100%);

  /* Comfortable Text Colors */
  --text-primary: #cdd6f4;
  --text-secondary: #bac2de;
  --text-muted: #9399b2;

  /* Soft Accent Colors (muted and comfortable) */
  --accent-blue: #89b4fa;
  --accent-green: #a6e3a1;
  --accent-yellow: #f9e2af;
  --accent-red: #f38ba8;
  --accent-purple: #cba6f7;
  --accent-teal: #94e2d5;

  /* Legacy color mappings for compatibility */
  --neon-cyan: var(--accent-teal);
  --neon-blue: var(--accent-blue);
  --neon-purple: var(--accent-purple);
  --neon-pink: var(--accent-red);
  --neon-green: var(--accent-green);
  --neon-yellow: var(--accent-yellow);
  --accent-cyan: var(--accent-teal);

  /* Subtle Border and Interaction Colors */
  --border-primary: #45475a;
  --border-hover: #585b70;
  --border-active: #6c7086;

  /* Removed aggressive glow effects - using subtle shadows instead */
  --shadow-subtle: 0 2px 8px rgb(0 0 0 / 0.3);
  --shadow-hover: 0 4px 12px rgb(0 0 0 / 0.4);

  /* RGB color values for glass effects */
  --bg-primary-rgb: 30, 30, 46;
  --bg-secondary-rgb: 49, 50, 68;
  --bg-tertiary-rgb: 88, 91, 112;
  --accent-blue-rgb: 137, 180, 250;
  --accent-purple-rgb: 203, 166, 247;
  --accent-red-rgb: 243, 139, 168;
  --accent-green-rgb: 166, 227, 161;

  /* Glass Effect Variables */
  --glass-blur-primary: blur(20px);
  --glass-blur-secondary: blur(12px);
  --glass-blur-tertiary: blur(8px);
  --glass-opacity-primary: 0.85;
  --glass-opacity-secondary: 0.75;
  --glass-opacity-tertiary: 0.65;
  --glass-border-primary: 1px solid rgb(255 255 255 / 0.2);
  --glass-border-secondary: 1px solid rgb(255 255 255 / 0.15);
  --glass-border-tertiary: 1px solid rgb(255 255 255 / 0.1);
  --glass-shadow-primary: 0 8px 32px rgb(0 0 0 / 0.12), 0 2px 8px rgb(0 0 0 / 0.08);
  --glass-shadow-secondary: 0 4px 16px rgb(0 0 0 / 0.1), 0 1px 4px rgb(0 0 0 / 0.06);
  --glass-shadow-tertiary: 0 2px 8px rgb(0 0 0 / 0.08), 0 1px 2px rgb(0 0 0 / 0.04);

  /* Spring Animation Variables */
  --spring-duration: 0.4s;
  --spring-easing: cubic-bezier(0.34, 1.56, 0.64, 1);
  --micro-spring: 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
