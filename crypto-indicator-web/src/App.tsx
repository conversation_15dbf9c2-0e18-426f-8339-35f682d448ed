import React from "react";

import { ErrorBoundary } from "./components/ErrorBoundary";
import StatisticsTable from "./components/StatisticsTable";
import { DarkReaderProvider } from "./components/ui/DarkReaderProvider";
import { ApiProvider } from "./context/ApiContext";
import { GameProvider } from "./context/GameContext";

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <DarkReaderProvider
        enabled
        config={{
          brightness: 150,
          contrast: 130,
          sepia: 0,
          grayscale: 0,
        }}
        autoDetectSystemTheme={false}
      >
        <ApiProvider>
          <GameProvider>
            <div>
              <StatisticsTable />
            </div>
          </GameProvider>
        </ApiProvider>
      </DarkReaderProvider>
    </ErrorBoundary>
  );
};

export default App;
