/**
 * Game Types
 * 
 * TypeScript types and interfaces for the Signal Prediction Challenge game
 */

export type SignalColor = 'gold' | 'blue' | 'gray';

export type GameMode = 'normal' | 'game';

export type PredictionState = 'waiting' | 'predicting' | 'revealed' | 'completed';

export interface Prediction {
  symbol: string;
  currency: string;
  predictedSignal: SignalColor;
  actualSignal: SignalColor;
  isCorrect: boolean;
  timeToPredict: number; // milliseconds
  points: number;
  timestamp: number;
}

export interface GameRound {
  id: string;
  symbol: string;
  currency: string;
  actualSignal: SignalColor;
  state: PredictionState;
  timeRemaining: number; // seconds
  startTime: number;
  prediction?: Prediction;
}

export interface CurrentRound {
  id: string;
  symbol: string;
  currency: string;
  actualSignal: SignalColor;
  state: PredictionState;
  timeRemaining: number;
  startTime: number;
  prediction?: Prediction;
}

export interface GameStats {
  totalRounds: number;
  correctPredictions: number;
  totalPoints: number;
  currentStreak: number;
  bestStreak: number;
  averageTime: number;
  accuracy: number;
}

export interface GameSession {
  id: string;
  startTime: number;
  endTime?: number;
  rounds: GameRound[];
  stats: GameStats;
  isActive: boolean;
}

export interface GameState {
  mode: GameMode;
  currentSession: GameSession | null;
  currentRound: GameRound | null;
  allTimeStats: GameStats;
  leaderboard: LeaderboardEntry[];
  isLoading: boolean;
  error: string | null;
}

export interface LeaderboardEntry {
  id: string;
  playerName: string;
  score: number;
  accuracy: number;
  rounds: number;
  date: number;
}

export interface GameConfig {
  roundTimeLimit: number; // seconds
  pointsPerCorrect: number;
  speedBonusThreshold: number; // seconds
  speedBonusPoints: number;
  streakBonusPoints: number;
  maxRoundsPerSession: number;
}

export const DEFAULT_GAME_CONFIG: GameConfig = {
  roundTimeLimit: 30,
  pointsPerCorrect: 100,
  speedBonusThreshold: 10,
  speedBonusPoints: 50,
  streakBonusPoints: 25,
  maxRoundsPerSession: 20,
};

export interface GameActions {
  startGame: () => void;
  endGame: () => void;
  startRound: (symbol: string, currency: string, actualSignal: SignalColor) => void;
  makePrediction: (predictedSignal: SignalColor) => void;
  skipRound: () => void;
  resetStats: () => void;
  setMode: (mode: GameMode) => void;
}
