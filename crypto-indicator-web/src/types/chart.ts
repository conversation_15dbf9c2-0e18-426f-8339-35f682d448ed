import type { CryptoCurrencyStatisticsDto } from "../generated";

export interface ChartData {
  time: string;
  value: number;
}

export interface CandlestickData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
}

export interface ChartModalProps {
  data: CryptoCurrencyStatisticsDto;
  onClose: () => void;
}

export interface ChartContainerProps {
  data: CryptoCurrencyStatisticsDto;
}
