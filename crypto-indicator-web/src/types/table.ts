export type SortColumn = 'symbol' | 'usdPrice' | 'marketCap' | 'usdSignal' | 'btcPrice' | 'btcSignal';
export type SortDirection = 'asc' | 'desc' | null;
export type SignalColor = 'gold' | 'blue' | 'gray' | 'all';

export interface SortConfig {
  column: SortColumn | null;
  direction: SortDirection;
}

export interface FilterConfig {
  symbolSearch: string;
  usdSignal: SignalColor;
  btcSignal: SignalColor;
}


