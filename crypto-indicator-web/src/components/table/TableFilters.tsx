import React from 'react';

import type { FilterConfig } from '../../types/table';

// Component-specific styles
import '../../styles/components/filters.css';

interface TableFiltersProps {
  filterConfig: FilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig['usdSignal']) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

// eslint-disable-next-line max-lines-per-function
export const TableFilters: React.FC<TableFiltersProps> = ({
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {

  return (
    <div className="table-filters">
      <div className="filters-row">
        <div className="filter-group">
          <label htmlFor="symbol-search">Search:</label>
          <input
            id="symbol-search"
            type="text"
            placeholder="Search cryptocurrencies..."
            value={filterConfig.symbolSearch}
            onChange={(e) => { onSymbolSearchChange(e.target.value); }}
            className="filter-input"
          />
        </div>

        <div className="filter-group">
          <label htmlFor="usd-signal">USD Signal:</label>
          <select
            id="usd-signal"
            value={filterConfig.usdSignal}
            onChange={(e) => { onUsdSignalChange(e.target.value as FilterConfig['usdSignal']); }}
            className="filter-select"
          >
            <option value="all">All</option>
            <option value="gold">Gold (Bullish)</option>
            <option value="blue">Blue (Bearish)</option>
            <option value="gray">Gray (Neutral)</option>
          </select>
        </div>

        <div className="filter-group">
          <label htmlFor="btc-signal">BTC Signal:</label>
          <select
            id="btc-signal"
            value={filterConfig.btcSignal}
            onChange={(e) => { onBtcSignalChange(e.target.value as FilterConfig['btcSignal']); }}
            className="filter-select"
          >
            <option value="all">All</option>
            <option value="gold">Gold (Bullish)</option>
            <option value="blue">Blue (Bearish)</option>
            <option value="gray">Gray (Neutral)</option>
          </select>
        </div>
        <div className="filter-actions">
          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="clear-filters-btn"
              type="button"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>

      <div className="filter-results">
        <span className="results-count">
          Showing {filteredCount} of {totalCount} cryptocurrencies
          {hasActiveFilters && <span className="filtered-indicator"> (filtered)</span>}
        </span>
      </div>
    </div>
  );
};
