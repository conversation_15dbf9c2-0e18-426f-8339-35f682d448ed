/**
 * Prediction Result Component
 * 
 * Shows the result of a prediction with animation and scoring details
 */

import React from 'react';

import { AnimatePresence, motion } from 'framer-motion';

import type { SignalColor } from '../../types/game';

// Constants
const SIGNAL_COLORS = {
  YELLOW: 'var(--accent-yellow)',
  BLUE: 'var(--accent-blue)',
  MUTED: 'var(--text-muted)',
} as const;

const MILLISECONDS_TO_SECONDS = 1000;
const ANIMATION_DELAY_STATUS = 0.2;
const ANIMATION_DELAY_SCORE = 0.4;
const SPRING_STIFFNESS = 200;

// Helper function
const getSignalInfo = (signal: SignalColor) => {
  switch (signal) {
    case 'gold': {
      return { label: 'Bullish', icon: '▲', color: SIGNAL_COLORS.YELLOW };
    }
    case 'blue': {
      return { label: 'Bearish', icon: '▼', color: SIGNAL_COLORS.BLUE };
    }
    case 'gray': {
      return { label: 'Neutral', icon: '●', color: SIGNAL_COLORS.MUTED };
    }
  }
};

// Results overlay for showing prediction results
interface PredictionResultProps {
  prediction: SignalColor;
  actual: SignalColor;
  isCorrect: boolean;
  points: number;
  timeToPredict: number;
  isVisible: boolean;
}

// Result status component
const ResultStatus: React.FC<{ isCorrect: boolean }> = ({ isCorrect }) => (
  <motion.div
    className={`result-status ${isCorrect ? 'correct' : 'incorrect'}`}
    initial={{ scale: 0 }}
    animate={{ scale: 1 }}
    transition={{ delay: ANIMATION_DELAY_STATUS, type: 'spring', stiffness: SPRING_STIFFNESS }}
  >
    {isCorrect ? '✓' : '✗'}
  </motion.div>
);

// Result details component
const ResultDetails: React.FC<{
  predictionInfo: { icon: string; label: string; color: string };
  actualInfo: { icon: string; label: string; color: string };
}> = ({ predictionInfo, actualInfo }) => (
  <div className="result-details">
    <div className="result-prediction">
      <span>Your prediction:</span>
      <span style={{ color: predictionInfo.color }}>
        {predictionInfo.icon} {predictionInfo.label}
      </span>
    </div>
    <div className="result-actual">
      <span>Actual signal:</span>
      <span style={{ color: actualInfo.color }}>
        {actualInfo.icon} {actualInfo.label}
      </span>
    </div>
  </div>
);

// Result score component
const ResultScore: React.FC<{ points: number; timeToPredict: number }> = ({ points, timeToPredict }) => (
  <div className="result-score">
    <motion.div
      className="points-earned"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ delay: ANIMATION_DELAY_SCORE, type: 'spring' }}
    >
      +{points} points
    </motion.div>
    <div className="time-taken">
      Predicted in {(timeToPredict / MILLISECONDS_TO_SECONDS).toFixed(1)}s
    </div>
  </div>
);

export const PredictionResult: React.FC<PredictionResultProps> = ({
  prediction,
  actual,
  isCorrect,
  points,
  timeToPredict,
  isVisible,
}) => {
  const predictionInfo = getSignalInfo(prediction);
  const actualInfo = getSignalInfo(actual);

  if (!isVisible) {return null;}

  return (
    <AnimatePresence>
      <motion.div
        className="prediction-result"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: 'easeOut' }}
      >
        <div className="result-content">
          <ResultStatus isCorrect={isCorrect} />
          <ResultDetails predictionInfo={predictionInfo} actualInfo={actualInfo} />
          <ResultScore points={points} timeToPredict={timeToPredict} />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
