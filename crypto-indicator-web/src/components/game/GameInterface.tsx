/**
 * Game Interface Component
 *
 * Main game interface that appears in the header when game mode is active.
 * Provides standalone game functionality without table integration.
 */

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

import { useGame } from '../../context/GameContext';
import type { SignalColor } from '../../types/game';

// Constants
const ROUND_TIME_LIMIT = 30;
const PREDICTION_BUTTONS = [
  { signal: 'gold' as SignalColor, icon: '▲', label: 'Bullish', color: 'var(--accent-yellow)' },
  { signal: 'blue' as SignalColor, icon: '▼', label: 'Bearish', color: 'var(--accent-blue)' },
  { signal: 'gray' as SignalColor, icon: '●', label: 'Neutral', color: 'var(--text-secondary)' },
];

// Timer component
const GameTimer: React.FC<{ timeRemaining: number }> = ({ timeRemaining }) => {
  const isUrgent = timeRemaining <= 10;
  
  return (
    <motion.div
      className="game-timer"
      animate={{
        scale: isUrgent ? [1, 1.1, 1] : 1,
        color: isUrgent ? 'var(--accent-red)' : 'var(--text-primary)',
      }}
      transition={{ duration: 0.5, repeat: isUrgent ? Infinity : 0 }}
    >
      {Math.ceil(timeRemaining)}s
    </motion.div>
  );
};

// Round info component
const RoundInfo: React.FC<{ symbol: string; currency: string; round: number; total: number }> = ({
  symbol,
  currency,
  round,
  total,
}) => (
  <div className="round-info">
    <div className="round-symbol">
      <span className="symbol-text">{symbol}/{currency}</span>
    </div>
    <div className="round-counter">
      Round {round}/{total}
    </div>
  </div>
);

// Prediction buttons component
const PredictionButtons: React.FC<{ onPrediction: (signal: SignalColor) => void }> = ({ onPrediction }) => (
  <div className="prediction-buttons-inline">
    {PREDICTION_BUTTONS.map((button) => (
      <motion.button
        key={button.signal}
        className="prediction-button-inline"
        onClick={() => onPrediction(button.signal)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        style={{
          ['--button-color' as string]: button.color,
        }}
      >
        <span className="prediction-icon">{button.icon}</span>
        <span className="prediction-label">{button.label}</span>
      </motion.button>
    ))}
  </div>
);

// Result display component
const PredictionResult: React.FC<{
  prediction: SignalColor;
  actual: SignalColor;
  isCorrect: boolean;
  points: number;
}> = ({ prediction, actual, isCorrect, points }) => (
  <motion.div
    className="prediction-result-inline"
    initial={{ opacity: 0, y: -10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: 10 }}
  >
    <div className={`result-status ${isCorrect ? 'correct' : 'incorrect'}`}>
      {isCorrect ? '✓ Correct!' : '✗ Incorrect'}
    </div>
    <div className="result-details">
      <span>You: {prediction}</span>
      <span>Actual: {actual}</span>
      <span>+{points} pts</span>
    </div>
  </motion.div>
);

// Main game interface component
export const GameInterface: React.FC = () => {
  const { mode, currentSession, currentRound, startRound, makePrediction } = useGame();
  const [timeRemaining, setTimeRemaining] = useState(ROUND_TIME_LIMIT);
  const [showResult, setShowResult] = useState(false);

  // Timer effect
  useEffect(() => {
    if (!currentRound || currentRound.state !== 'predicting') return;

    const timer = setInterval(() => {
      const elapsed = (Date.now() - currentRound.startTime) / 1000;
      const remaining = Math.max(0, ROUND_TIME_LIMIT - elapsed);
      setTimeRemaining(remaining);

      if (remaining <= 0) {
        // Auto-skip round if time runs out
        clearInterval(timer);
      }
    }, 100);

    return () => clearInterval(timer);
  }, [currentRound]);

  // Auto-start first round when game starts
  useEffect(() => {
    if (mode === 'game' && currentSession && !currentRound) {
      // Start with a demo round using random signal
      const signals: SignalColor[] = ['gold', 'blue', 'gray'];
      const randomSignal = signals[Math.floor(Math.random() * signals.length)] as SignalColor;
      startRound('BTC', 'USD', randomSignal);
    }
  }, [mode, currentSession, currentRound, startRound]);

  // Show result when prediction is made
  useEffect(() => {
    if (currentRound?.prediction) {
      setShowResult(true);
      const timer = setTimeout(() => {
        setShowResult(false);
        // Start next round after showing result
        if (currentSession) {
          const signals: SignalColor[] = ['gold', 'blue', 'gray'];
          const cryptos = ['BTC', 'ETH', 'ADA', 'DOT', 'SOL'];
          const randomCrypto = cryptos[Math.floor(Math.random() * cryptos.length)] as string;
          const randomSignalNext = signals[Math.floor(Math.random() * signals.length)] as SignalColor;
          startRound(randomCrypto, 'USD', randomSignalNext);
        }
      }, 3000);

      return () => {
        clearTimeout(timer);
      };
    }

    return;
  }, [currentRound?.prediction, currentSession, startRound]);

  const handlePrediction = (signal: SignalColor) => {
    makePrediction(signal);
  };

  // Don't render if not in game mode
  if (mode !== 'game' || !currentSession) {
    return null;
  }

  return (
    <motion.div
      className="game-interface"
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="game-interface-content">
        {currentRound && (
          <>
            <div className="game-interface-header">
              <RoundInfo
                symbol={currentRound.symbol}
                currency={currentRound.currency}
                round={currentSession.rounds.length + 1}
                total={20}
              />
              <GameTimer timeRemaining={timeRemaining} />
            </div>

            <AnimatePresence mode="wait">
              {showResult && currentRound.prediction ? (
                <PredictionResult
                  key="result"
                  prediction={currentRound.prediction.predictedSignal}
                  actual={currentRound.prediction.actualSignal}
                  isCorrect={currentRound.prediction.isCorrect}
                  points={currentRound.prediction.points}
                />
              ) : currentRound.state === 'predicting' ? (
                <div key="prediction" className="game-interface-prediction">
                  <div className="prediction-prompt">
                    Predict the signal for {currentRound.symbol}:
                  </div>
                  <PredictionButtons onPrediction={handlePrediction} />
                </div>
              ) : null}
            </AnimatePresence>
          </>
        )}
      </div>
    </motion.div>
  );
};
