import React from "react";

interface ErrorStateProps {
  message: string;
  onRetry?: () => void;
  showRetry?: boolean;
}
export const ErrorState: React.FC<ErrorStateProps> = ({
  message,
  onRetry,
  showRetry = true,
}) => {
  return (
    <div className="error-container">
      <div className="error-icon">⚠️</div>
      <p className="error-message">{message}</p>
      {showRetry && onRetry && (
        <button onClick={onRetry} className="retry-button">
          Try Again
        </button>
      )}
    </div>
  );
};
