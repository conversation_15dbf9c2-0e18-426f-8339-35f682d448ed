import React, { useState } from 'react';

import { getSignalInfo } from '../../utils/signalHelpers';
import type { SignalColor } from '../../types/game';

// Component-specific styles
import '../../styles/components/signal-badge.css';

interface SignalBadgeProps {
  color?: string | undefined;
  onClick?: () => void;
  clickable?: boolean;
  title?: string;
  loading?: boolean;
  disabled?: boolean;
}

// Helper component for rendering the complete signal badge
const SignalBadgeRenderer: React.FC<{
  signalInfo: SignalInfo;
  isInteractive: boolean;
  className: string;
  displayTitle: string;
  isGameMode: boolean;
  currentRound: GameRound | null;
  showTooltip: boolean;
  handleClick: () => void;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  disabled: boolean;
  loading: boolean;
  finalAriaLabel: string;
  shouldShowOverlays: boolean;
  color?: string | undefined;
  symbol?: string | undefined;
  currency?: string | undefined;
  showPredictionOverlay: boolean;
  setShowPredictionOverlay: (show: boolean) => void;
  showResult: boolean;
}> = (props) => (
  <>
    <SignalBadgeSpan
      className={props.className}
      handleClick={props.handleClick}
      handleMouseEnter={props.handleMouseEnter}
      handleMouseLeave={props.handleMouseLeave}
      handleKeyDown={props.handleKeyDown}
      isInteractive={props.isInteractive}
      isGameMode={props.isGameMode}
      disabled={props.disabled}
      loading={props.loading}
      finalAriaLabel={props.finalAriaLabel}
    >
      <SignalBadgeContent
        loading={props.loading}
        isGameMode={props.isGameMode}
        currentRound={props.currentRound}
        signalInfo={props.signalInfo}
        displayTitle={props.displayTitle}
        showTooltip={props.showTooltip}
      />
    </SignalBadgeSpan>

    <GameOverlays
      shouldShowOverlays={props.shouldShowOverlays}
      color={props.color}
      symbol={props.symbol}
      currency={props.currency}
      showPredictionOverlay={props.showPredictionOverlay}
      setShowPredictionOverlay={props.setShowPredictionOverlay}
      currentRound={props.currentRound}
      showResult={props.showResult}
    />
  </>
);

export const SignalBadge: React.FC<SignalBadgeProps> = (props) => {
  const data = useSignalBadgeData(props);

  return (
    <SignalBadgeRenderer
      signalInfo={data.signalInfo}
      isInteractive={data.isInteractive}
      className={data.className}
      displayTitle={data.displayTitle}
      isGameMode={data.isGameMode}
      currentRound={data.currentRound}
      showTooltip={data.showTooltip}
      handleClick={data.handleClick}
      handleMouseEnter={data.handleMouseEnter}
      handleMouseLeave={data.handleMouseLeave}
      handleKeyDown={data.handleKeyDown}
      disabled={data.disabled}
      loading={data.loading}
      finalAriaLabel={data.finalAriaLabel}
      shouldShowOverlays={data.shouldShowOverlays}
      color={data.color}
      symbol={data.symbol}
      currency={data.currency}
      showPredictionOverlay={data.showPredictionOverlay}
      setShowPredictionOverlay={data.setShowPredictionOverlay}
      showResult={data.showResult}
    />
  );
};
