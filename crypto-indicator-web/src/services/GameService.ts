/**
 * Game Service
 * 
 * Business logic for the Signal Prediction Challenge game
 * Handles scoring, statistics, and game state management
 */

import { DEFAULT_GAME_CONFIG } from '../types/game';

import type {
  GameConfig,
  GameRound,
  GameSession,
  GameStats,
  LeaderboardEntry,
  Prediction,
  SignalColor,
} from '../types/game';

// Constants
const RANDOM_ID_BASE = 36;
const RANDOM_ID_SLICE_END = 11;
const SIGNAL_TYPES_COUNT = 3;
const DIFFICULTY_MULTIPLIER = 0.5;

export class GameService {
  private readonly config: GameConfig;

  constructor(config: GameConfig = DEFAULT_GAME_CONFIG) {
    this.config = config;
  }

  /**
   * Create a new game session
   */
  createSession(): GameSession {
    return {
      id: this.generateId(),
      startTime: Date.now(),
      rounds: [],
      stats: this.createEmptyStats(),
      isActive: true,
    };
  }

  /**
   * Create a new game round
   */
  createRound(symbol: string, currency: string, actualSignal: SignalColor): GameRound {
    return {
      id: this.generateId(),
      symbol,
      currency,
      actualSignal,
      state: 'predicting',
      timeRemaining: this.config.roundTimeLimit,
      startTime: Date.now(),
    };
  }

  /**
   * Calculate points for a prediction
   */
  calculatePoints(
    isCorrect: boolean,
    timeToPredict: number,
    currentStreak: number
  ): number {
    if (!isCorrect) {return 0;}

    let points = this.config.pointsPerCorrect;

    // Speed bonus
    if (timeToPredict < this.config.speedBonusThreshold * 1000) {
      points += this.config.speedBonusPoints;
    }

    // Streak bonus
    if (currentStreak > 0) {
      points += currentStreak * this.config.streakBonusPoints;
    }

    return points;
  }

  /**
   * Process a prediction and update statistics
   */
  processPrediction(
    round: GameRound,
    predictedSignal: SignalColor,
    currentStats: GameStats
  ): { prediction: Prediction; updatedStats: GameStats } {
    const timeToPredict = Date.now() - round.startTime;
    const isCorrect = predictedSignal === round.actualSignal;
    
    const points = this.calculatePoints(
      isCorrect,
      timeToPredict,
      currentStats.currentStreak
    );

    const prediction: Prediction = {
      symbol: round.symbol,
      currency: round.currency,
      predictedSignal,
      actualSignal: round.actualSignal,
      isCorrect,
      timeToPredict,
      points,
      timestamp: Date.now(),
    };

    const updatedStats = this.updateStats(currentStats, prediction);

    return { prediction, updatedStats };
  }

  /**
   * Update game statistics
   */
  updateStats(stats: GameStats, prediction: Prediction): GameStats {
    const newTotalRounds = stats.totalRounds + 1;
    const newCorrectPredictions = stats.correctPredictions + (prediction.isCorrect ? 1 : 0);
    const newTotalPoints = stats.totalPoints + prediction.points;
    
    const newCurrentStreak = prediction.isCorrect ? stats.currentStreak + 1 : 0;
    const newBestStreak = Math.max(stats.bestStreak, newCurrentStreak);
    
    const totalTime = (stats.averageTime * stats.totalRounds) + prediction.timeToPredict;
    const newAverageTime = totalTime / newTotalRounds;
    
    const newAccuracy = (newCorrectPredictions / newTotalRounds) * 100;

    return {
      totalRounds: newTotalRounds,
      correctPredictions: newCorrectPredictions,
      totalPoints: newTotalPoints,
      currentStreak: newCurrentStreak,
      bestStreak: newBestStreak,
      averageTime: newAverageTime,
      accuracy: newAccuracy,
    };
  }

  /**
   * Create empty statistics object
   */
  createEmptyStats(): GameStats {
    return {
      totalRounds: 0,
      correctPredictions: 0,
      totalPoints: 0,
      currentStreak: 0,
      bestStreak: 0,
      averageTime: 0,
      accuracy: 0,
    };
  }

  /**
   * Check if session should end
   */
  shouldEndSession(session: GameSession): boolean {
    return session.rounds.length >= this.config.maxRoundsPerSession;
  }

  /**
   * Finalize a game session
   */
  finalizeSession(session: GameSession): GameSession {
    return {
      ...session,
      endTime: Date.now(),
      isActive: false,
    };
  }

  /**
   * Create leaderboard entry from session
   */
  createLeaderboardEntry(session: GameSession, playerName = 'Anonymous'): LeaderboardEntry {
    return {
      id: this.generateId(),
      playerName,
      score: session.stats.totalPoints,
      accuracy: session.stats.accuracy,
      rounds: session.stats.totalRounds,
      date: session.endTime ?? Date.now(),
    };
  }

  /**
   * Sort leaderboard entries
   */
  sortLeaderboard(entries: LeaderboardEntry[]): LeaderboardEntry[] {
    return [...entries].sort((a, b) => {
      // Primary sort: score (descending)
      if (a.score !== b.score) {return b.score - a.score;}
      
      // Secondary sort: accuracy (descending)
      if (a.accuracy !== b.accuracy) {return b.accuracy - a.accuracy;}
      
      // Tertiary sort: date (most recent first)
      return b.date - a.date;
    });
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(RANDOM_ID_BASE).slice(2, RANDOM_ID_SLICE_END)}`;
  }

  /**
   * Format time in milliseconds to seconds
   */
  formatTime(milliseconds: number): string {
    return (milliseconds / 1000).toFixed(1);
  }

  /**
   * Format accuracy percentage
   */
  formatAccuracy(accuracy: number): string {
    return `${accuracy.toFixed(1)}%`;
  }

  /**
   * Get difficulty multiplier based on signal distribution
   */
  getDifficultyMultiplier(signals: SignalColor[]): number {
    if (signals.length === 0) {return 1;}

    const counts = signals.reduce((acc, signal) => {
      acc[signal] = (acc[signal] ?? 0) + 1;
      return acc;
    }, {} as Record<SignalColor, number>);

    const total = signals.length;
    const distribution = Object.values(counts).map(count => count / total);

    // More balanced distribution = higher difficulty
    const entropy = -distribution.reduce((sum, p) => sum + (p * Math.log2(p)), 0);
    const maxEntropy = Math.log2(SIGNAL_TYPES_COUNT);

    return 1 + (entropy / maxEntropy) * DIFFICULTY_MULTIPLIER;
  }
}
