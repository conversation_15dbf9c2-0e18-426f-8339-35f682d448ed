import type { CryptoCurrencyStatisticsDto, IndicatorValueDto } from '../generated';
import type { SortColumn, SortConfig, SortDirection } from '../types/table';

const SIGNAL_PRIORITY = {
  gold: 3,
  blue: 2,
  gray: 1,
  null: 0,
  undefined: 0,
} as const;

export const getSignalPriority = (color: string | null | undefined): number => {
  if (color === null || color === undefined || color === "") {
    return SIGNAL_PRIORITY.null;
  }
  return SIGNAL_PRIORITY[color as keyof typeof SIGNAL_PRIORITY] || SIGNAL_PRIORITY.null;
};

export const getSortValue = (
  crypto: CryptoCurrencyStatisticsDto,
  btcData: IndicatorValueDto | undefined,
  column: SortColumn
): string | number => {
  const usdData = crypto.indicatorValues?.[0];

  switch (column) {
    case 'symbol': {
      return crypto.symbol ?? '';
    }
    case 'usdPrice': {
      return usdData?.close ?? 0;
    }
    case 'marketCap': {
      return usdData?.marketCap ?? 0;
    }
    case 'usdSignal': {
      return getSignalPriority(usdData?.color);
    }
    case 'btcPrice': {
      return btcData?.close ?? 0;
    }
    case 'btcSignal': {
      return getSignalPriority(btcData?.color);
    }
    default: {
      return '';
    }
  }
};

const handleNullValues = (a: string | number | null, b: string | number | null, direction: SortDirection): number | null => {
  if (a === null && b === null) {
    return 0;
  }
  if (a === null) {
    return direction === 'asc' ? 1 : -1;
  }
  if (b === null) {
    return direction === 'asc' ? -1 : 1;
  }
  return null; // Continue with normal comparison
};

const compareTypedValues = (a: string | number, b: string | number): number => {
  if (typeof a === 'string' && typeof b === 'string') {
    return a.localeCompare(b);
  }
  if (typeof a === 'number' && typeof b === 'number') {
    return a - b;
  }
  // Handle mixed types
  return String(a).localeCompare(String(b));
};

export const compareValues = (a: string | number, b: string | number, direction: SortDirection): number => {
  if (direction === null) {
    return 0;
  }

  // Handle null/undefined values
  const nullResult = handleNullValues(a, b, direction);
  if (nullResult !== null) {
    return nullResult;
  }

  // Compare values
  const result = compareTypedValues(a, b);
  return direction === 'asc' ? result : -result;
};

export const applySorting = (
  data: CryptoCurrencyStatisticsDto[],
  btcStatistics: CryptoCurrencyStatisticsDto[],
  sortConfig: SortConfig,
  findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined
): CryptoCurrencyStatisticsDto[] => {
  if (!sortConfig.column || !sortConfig.direction) {
    return data;
  }

  return [...data].sort((a, b) => {
    const btcDataA = findBtcDataForSymbol(btcStatistics, a.symbol);
    const btcDataB = findBtcDataForSymbol(btcStatistics, b.symbol);

    const {column} = sortConfig;
    if (column === null || column === undefined) {
      return 0;
    }

    const valueA = getSortValue(a, btcDataA, column);
    const valueB = getSortValue(b, btcDataB, column);

    const { direction } = sortConfig;
    const primaryResult = compareValues(valueA, valueB, direction);

    // Secondary sort by symbol for stable sorting
    if (primaryResult === 0) {
      return a.symbol.localeCompare(b.symbol);
    }

    return primaryResult;
  });
};
