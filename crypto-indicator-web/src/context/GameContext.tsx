/**
 * Game Context
 * 
 * React context for managing Signal Prediction Challenge game state
 * Uses Zustand for state management with React integration
 */

import React, { createContext, ReactNode,useContext } from 'react';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { GameService } from '../services/GameService';
import { DEFAULT_GAME_CONFIG } from '../types/game';

import type {
  GameActions,
  GameMode,
  GameRound,
  GameSession,
  GameState,
  GameStats,
  LeaderboardEntry,
  SignalColor,
} from '../types/game';

// Game service instance
const gameService = new GameService(DEFAULT_GAME_CONFIG);

// Constants
const LEADERBOARD_SIZE = 10;
const RESULT_DISPLAY_TIMEOUT = 3000;
const MILLISECONDS_PER_SECOND = 1000;
const SESSION_END_DELAY = 1000;

// Helper functions
const updateAllTimeStats = (allTimeStats: GameStats, rounds: GameRound[]): GameStats => {
  let updatedStats = allTimeStats;
  for (const round of rounds) {
    if (round.prediction !== undefined) {
      updatedStats = gameService.updateStats(updatedStats, round.prediction);
    }
  }
  return updatedStats;
};

const createLeaderboardEntry = (session: GameSession): LeaderboardEntry => {
  return gameService.createLeaderboardEntry(session);
};

// Helper function for ending game
const createEndGameAction = (get: () => GameStore, set: (state: Partial<GameStore>) => void) => () => {
  const { currentSession, allTimeStats, leaderboard } = get();

  if (!currentSession) {return;}

  const finalizedSession = gameService.finalizeSession(currentSession);
  const updatedAllTimeStats = updateAllTimeStats(allTimeStats, finalizedSession.rounds);
  const leaderboardEntry = createLeaderboardEntry(finalizedSession);
  const updatedLeaderboard = gameService.sortLeaderboard([
    ...leaderboard,
    leaderboardEntry,
  ]).slice(0, LEADERBOARD_SIZE);

  set({
    mode: 'normal',
    currentSession: null,
    currentRound: null,
    allTimeStats: updatedAllTimeStats,
    leaderboard: updatedLeaderboard,
  });
};

// Helper function for starting round
const createStartRoundAction = (get: () => GameStore, set: (state: Partial<GameStore>) => void) =>
  (symbol: string, currency: string, actualSignal: SignalColor) => {
    const { currentSession } = get();

    if (currentSession?.isActive !== true) {return;}

    const round = gameService.createRound(symbol, currency, actualSignal);

    set({
      currentRound: round,
      currentSession: {
        ...currentSession,
        rounds: [...currentSession.rounds, round],
      },
    });

    // Auto-skip round after time limit
    setTimeout(() => {
      const { currentRound: currentRoundCheck } = get();
      if (currentRoundCheck?.id === round.id && currentRoundCheck?.state === 'predicting') {
        get().skipRound();
      }
    }, DEFAULT_GAME_CONFIG.roundTimeLimit * MILLISECONDS_PER_SECOND);
  };

// Helper function for making prediction
const createMakePredictionAction = (get: () => GameStore, set: (state: Partial<GameStore>) => void) =>
  (predictedSignal: SignalColor) => {
    const { currentRound, currentSession } = get();

    if (!currentRound || !currentSession || currentRound.state !== 'predicting') {return;}

    const { prediction, updatedStats } = gameService.processPrediction(
      currentRound,
      predictedSignal,
      currentSession.stats
    );

    const updatedRound: GameRound = {
      ...currentRound,
      state: 'revealed',
      prediction,
    };

    const updatedSession: GameSession = {
      ...currentSession,
      stats: updatedStats,
      rounds: currentSession.rounds.map(round =>
        round.id === currentRound.id ? updatedRound : round
      ),
    };

    set({
      currentRound: updatedRound,
      currentSession: updatedSession,
    });

    // Auto-complete round after showing result
    setTimeout(() => {
      const { currentRound: currentRoundCheck } = get();
      if (currentRoundCheck?.id === updatedRound.id) {
        set({
          currentRound: {
            ...currentRoundCheck,
            state: 'completed',
          },
        });

        // Check if session should end
        if (gameService.shouldEndSession(updatedSession)) {
          setTimeout(() => { get().endGame(); }, SESSION_END_DELAY);
        }
      }
    }, RESULT_DISPLAY_TIMEOUT);
  };

// Zustand store for game state
interface GameStore extends GameState, GameActions {}

const useGameStore = create<GameStore>()(
  persist(
    (set, get) => ({
      // Initial state
      mode: 'normal',
      currentSession: null,
      currentRound: null,
      allTimeStats: gameService.createEmptyStats(),
      leaderboard: [],
      isLoading: false,
      error: null,

      // Actions
      startGame: () => {
        const session = gameService.createSession();
        set({
          mode: 'game',
          currentSession: session,
          currentRound: null,
          isLoading: false,
          error: null,
        });
      },

      endGame: createEndGameAction(get, set),
      startRound: createStartRoundAction(get, set),
      makePrediction: createMakePredictionAction(get, set),

      skipRound: () => {
        const { currentRound } = get();

        if (!currentRound || currentRound.state !== 'predicting') {return;}

        set({
          currentRound: {
            ...currentRound,
            state: 'completed',
          },
        });
      },

      resetStats: () => {
        set({
          allTimeStats: gameService.createEmptyStats(),
          leaderboard: [],
        });
      },

      setMode: (mode: GameMode) => {
        if (mode === 'normal') {
          get().endGame();
        }
        set({ mode });
      },
    }),
    {
      name: 'signal-prediction-game',
      partialize: (state) => ({
        allTimeStats: state.allTimeStats,
        leaderboard: state.leaderboard,
      }),
    }
  )
);

// React context for game store
const GameContext = createContext<GameStore | null>(null);

interface GameProviderProps {
  children: ReactNode;
}

/**
 * Game Context Provider
 * Provides game state and actions to child components
 */
export const GameProvider: React.FC<GameProviderProps> = ({ children }) => {
  const gameStore = useGameStore();

  return (
    <GameContext.Provider value={gameStore}>
      {children}
    </GameContext.Provider>
  );
};

/**
 * Hook to access game state and actions
 * Throws error if used outside of GameProvider
 */
export const useGame = (): GameStore => {
  const context = useContext(GameContext);
  
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  
  return context;
};

/**
 * Hook to access only game state (for read-only components)
 */
export const useGameState = (): GameState => {
  const game = useGame();
  
  return {
    mode: game.mode,
    currentSession: game.currentSession,
    currentRound: game.currentRound,
    allTimeStats: game.allTimeStats,
    leaderboard: game.leaderboard,
    isLoading: game.isLoading,
    error: game.error,
  };
};

/**
 * Hook to access only game actions
 */
export const useGameActions = (): GameActions => {
  const game = useGame();
  
  return {
    startGame: game.startGame,
    endGame: game.endGame,
    startRound: game.startRound,
    makePrediction: game.makePrediction,
    skipRound: game.skipRound,
    resetStats: game.resetStats,
    setMode: game.setMode,
  };
};
