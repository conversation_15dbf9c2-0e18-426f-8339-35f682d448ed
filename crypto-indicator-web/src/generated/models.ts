export interface IndicatorValueDto {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  marketCap: number;
  timestamp: string;
  name: string;
  hl2: number;
  p1: boolean;
  p2: boolean;
  p3: boolean;
  color: 'gold' | 'blue' | 'gray';
  smma_15: number;
  smma_19: number;
  smma_25: number;
  smma_29: number;
}

export interface CryptocurrencyMappingDto {
  symbol: string;
  name: string;
  slug: string;
  is_active: boolean;
  first_historical_data: string;
  last_historical_data: string;
  id: number;
  rank: number;
}

export interface CryptoCurrencyStatisticsDto {
  symbol: string;
  conversionCurrency: string;
  indicatorValues: IndicatorValueDto[];
  mapping: CryptocurrencyMappingDto;
}

