export interface ApiConfig {
  basePath?: string;
  apiKey?: string;
  headers?: Record<string, string>;
}

export class BaseCryptoIndicatorApiClient {
  private basePath: string;
  private headers: Record<string, string>;

  constructor(config: ApiConfig = {}) {
    // Default to empty string for development proxy, or current origin for production
    this.basePath = config.basePath ?? (
      process.env['NODE_ENV'] === 'development'
        ? ''
        : (process.env['REACT_APP_API_BASE_URL'] ?? window.location.origin)
    );
    this.headers = {
      'Content-Type': 'application/json',
      ...config.headers,
    };

    if (config.apiKey) {
      this.headers['Authorization'] = `Bearer ${  config.apiKey}`;
    }
  }

  protected async request<T>(
    method: string,
    path: string,
    body?: any
  ): Promise<T> {
    const url = this.basePath + path;

    const options: RequestInit = {
      method,
      headers: this.headers,
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      throw new Error(`API Error: ${  response.status  } ${  response.statusText}`);
    }

    return response.json() as Promise<T>;
  }
}
