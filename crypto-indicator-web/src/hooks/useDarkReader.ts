import { useEffect, useState } from 'react';

import * as <PERSON>Read<PERSON> from 'darkreader';

interface DarkReaderConfig {
  brightness: number;
  contrast: number;
  sepia: number;
  grayscale?: number;
}

interface UseDarkReaderOptions {
  enabled?: boolean;
  config?: DarkReaderConfig;
  autoDetectSystemTheme?: boolean;
}

const DEFAULT_CONFIG: DarkReaderConfig = {
  brightness: 105,
  contrast: 95,
  sepia: 8,
  grayscale: 0,
};

// eslint-disable-next-line max-lines-per-function
export const useDarkReader = (options: UseDarkReaderOptions = {}) => {
  const {
    enabled = true,
    config = DEFAULT_CONFIG,
    autoDetectSystemTheme = false,
  } = options;

  const [isEnabled, setIsEnabled] = useState(false);
  const [currentConfig, setCurrentConfig] = useState(config);

  useEffect(() => {
    if (!enabled) {
      DarkReader.disable();
      setIsEnabled(false);
      return;
    }

    if (autoDetectSystemTheme) {
      // Use DarkReader's auto mode to follow system color scheme
      DarkReader.auto(currentConfig);
    } else {
      // Always enable DarkReader with the specified config
      DarkReader.enable(currentConfig);
    }

    setIsEnabled(true);

    // Cleanup function
    return () => {
      DarkReader.disable();
    };
  }, [enabled, currentConfig, autoDetectSystemTheme]);

  const enable = (newConfig?: DarkReaderConfig) => {
    const configToUse = newConfig ?? currentConfig;
    DarkReader.enable(configToUse);
    setCurrentConfig(configToUse);
    setIsEnabled(true);
  };

  const disable = () => {
    DarkReader.disable();
    setIsEnabled(false);
  };

  const toggle = () => {
    if (isEnabled) {
      disable();
    } else {
      enable();
    }
  };

  const updateConfig = (newConfig: Partial<DarkReaderConfig>) => {
    const updatedConfig = { ...currentConfig, ...newConfig };
    setCurrentConfig(updatedConfig);
    if (isEnabled) {
      DarkReader.enable(updatedConfig);
    }
  };

  const exportCSS = async (): Promise<string> => {
    return await DarkReader.exportGeneratedCSS();
  };

  return {
    isEnabled,
    config: currentConfig,
    enable,
    disable,
    toggle,
    updateConfig,
    exportCSS,
    isDarkReaderSupported: DarkReader !== undefined,
  };
};


